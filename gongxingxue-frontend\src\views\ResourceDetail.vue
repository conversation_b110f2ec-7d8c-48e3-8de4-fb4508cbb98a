<template>
  <div class="resource-detail" v-loading="loading">
    <template v-if="resource">
      <div class="resource-header">
        <div class="resource-title">
          <span class="exam-type-tag" :class="`exam-type-${resource.examType}`">{{ examTypeText }}</span>
          <h1>{{ resource.name }}</h1>
        </div>

        <div class="resource-meta">
          <div class="meta-item">
            <el-icon><el-icon-download /></el-icon>
            <span>{{ resource.downloadCount }} 下载</span>
          </div>
          <div class="meta-item">
            <el-icon><el-icon-chat-dot-round /></el-icon>
            <span>{{ resource.commentCount }} 评论</span>
          </div>
          <div class="meta-item">
            <el-icon><el-icon-clock /></el-icon>
            <span>{{ formatDate(resource.auditTime) }}</span>
          </div>
        </div>
      </div>

      <div class="resource-actions">
        <el-button type="primary" @click="handleDownload">
          <el-icon><el-icon-download /></el-icon>
          下载资料
        </el-button>
        <el-button type="success" @click="handleView">
          <el-icon><el-icon-view /></el-icon>
          在线查看
        </el-button>
      </div>

      <div class="resource-description card">
        <h3>资料简介</h3>
        <p>{{ resource.description || '暂无简介' }}</p>
      </div>

      <div class="resource-comments card">
        <h3>评论区 ({{ resource.commentCount }})</h3>

        <div class="comment-form" v-if="isLoggedIn">
          <el-input
            v-model="commentContent"
            type="textarea"
            :rows="3"
            placeholder="发表您的评论..."
            maxlength="500"
            show-word-limit
          />
          <div class="comment-form-actions">
            <el-button type="primary" :disabled="!commentContent.trim()" @click="submitComment">
              发表评论
            </el-button>
          </div>
        </div>

        <div class="login-to-comment" v-else>
          <router-link to="/login">登录</router-link> 后参与评论
        </div>

        <div class="comments-list" v-loading="commentsLoading">
          <template v-if="comments.length > 0">
            <comment-item
              v-for="comment in comments"
              :key="`${comment.id}-${commentsRefreshKey}`"
              :comment="comment"
              @reply="handleReply"
              @delete="handleDeleteComment"
            />

            <div class="pagination" v-if="commentTotal > pageSize">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="commentTotal"
                :page-size="pageSize"
                :current-page="currentPage"
                @current-change="handlePageChange"
              />
            </div>
          </template>

          <el-empty v-else description="暂无评论" />
        </div>
      </div>
    </template>

    <el-empty v-else-if="!loading" description="资料不存在或已被删除" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '../store/user'
import { getResourceById, downloadResource, getDownloadUrl, getViewUrl } from '../api/resource'
import { useAuth } from '../composables/useAuth'
import { getCommentsByResourceId, addComment, deleteComment, getRepliesByParentId } from '../api/comment'
import { formatDate, getToken } from '../utils/auth'
import CommentItem from '../components/CommentItem.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const { isLoggedIn, checkAuthAndExecute, getActionConfig } = useAuth()

// Data
const resource = ref(null)
const loading = ref(false)
const comments = ref([])
const commentsLoading = ref(false)
const commentTotal = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const commentContent = ref('')
const replyToComment = ref(null)
const commentsRefreshKey = ref(0)

// Computed
const resourceId = computed(() => Number(route.params.id))
const targetCommentId = computed(() => route.query.commentId ? Number(route.query.commentId) : null)
const examTypeText = computed(() => {
  if (!resource.value) return ''
  const examTypes = ['考研', '考公', '法考', '教资', '其他']
  return examTypes[resource.value.examType] || '未知'
})

// Fetch resource details
const fetchResourceDetails = async () => {
  loading.value = true
  try {
    // 🎯 使用统一的API - 支持匿名和登录用户访问
    const response = await getResourceById(resourceId.value)
    resource.value = response.data
  } catch (error) {
    console.error('Failed to fetch resource details:', error)
    ElMessage.error('获取资料详情失败')
  } finally {
    loading.value = false
  }
}

// Fetch comments
const fetchComments = async () => {
  commentsLoading.value = true
  try {
    const response = await getCommentsByResourceId(resourceId.value, currentPage.value, pageSize.value)
    comments.value = response.data.records
    commentTotal.value = response.data.total
    // Force refresh all comment components to reload their replies
    commentsRefreshKey.value++

    // 如果有目标评论ID，尝试定位到该评论
    if (targetCommentId.value) {
      console.log('Found target comment ID in URL:', targetCommentId.value)
      await nextTick()
      scrollToTargetComment()
    } else {
      console.log('No target comment ID in URL, route.query:', route.query)
    }
  } catch (error) {
    console.error('Failed to fetch comments:', error)
  } finally {
    commentsLoading.value = false
  }
}

// Handle download
const handleDownload = async () => {
  // 🎯 使用统一的权限检查和API
  return await checkAuthAndExecute(async () => {
    try {
      // 使用统一的下载API，自动处理认证
      const response = await downloadResource(resourceId.value)

      // 验证响应
      if (!response || !response.data) {
        throw new Error('下载响应为空')
      }

      // response.data 是 blob 数据
      const blob = response.data

      // 验证blob是否有效
      if (!(blob instanceof Blob) || blob.size === 0) {
        throw new Error('下载的文件为空或格式错误')
      }

      console.log('Downloaded blob size:', blob.size, 'bytes')
      console.log('Downloaded blob type:', blob.type)

      // 获取文件名
      let filename = resource.value.originalFilename || resource.value.name || `resource_${resourceId.value}`

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename

      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)

      // 不显示额外提示，让浏览器自己处理下载体验
      // 用户可以在浏览器的下载栏中看到下载进度和结果
      console.log('Download initiated for:', filename, 'Size:', blob.size, 'bytes')
    } catch (error) {
      console.error('Download failed:', error)
      ElMessage.error('下载失败：' + (error.message || '请稍后重试'))
    }
  }, { actionName: '下载资料' })
}

// Handle view
const handleView = async () => {
  const filename = resource.value.originalFilename || resource.value.name
  const extension = filename.split('.').pop()?.toLowerCase()

  // Check if file can be previewed in browser
  const previewableExtensions = ['pdf', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'mp4', 'mp3']

  if (previewableExtensions.includes(extension)) {
    // 需要登录才能预览
    return await checkAuthAndExecute(async () => {
      try {
        // 使用配置文件中的API地址
        const { getFileBaseUrl } = await import('@/utils/config')
        const baseURL = getFileBaseUrl()
        const viewUrl = `${baseURL}/api/resources/view/${resourceId.value}`

        // 获取token并构建带token的URL
        const token = getToken()
        if (token) {
          // 在新窗口中打开带token的URL
          const urlWithToken = `${viewUrl}?token=${encodeURIComponent(token)}`
          window.open(urlWithToken, '_blank')
        } else {
          ElMessage.error('请先登录后再预览')
        }
      } catch (error) {
        console.error('Preview failed:', error)
        ElMessage.error('预览失败：' + (error.message || '请稍后重试'))
      }
    }, { actionName: '预览资料' })
  } else {
    // Cannot be previewed - show message and offer download after delay
    ElMessage.warning({
      message: `${extension.toUpperCase()} 文件无法在线预览，将在 2 秒后自动下载`,
      duration: 2000,
      showClose: true
    })

    // Delay download to let user see the message
    setTimeout(() => {
      handleDownload()
    }, 2000)
  }
}

// Submit comment
const submitComment = async () => {
  if (!commentContent.value.trim()) return

  // 🎯 使用统一的权限检查
  return await checkAuthAndExecute(async () => {
    try {
      const data = {
        resourceId: resourceId.value,
        content: commentContent.value
      }

      if (replyToComment.value) {
        data.parentId = replyToComment.value.id
      }

      await addComment(data)

      ElMessage.success(replyToComment.value ? '回复成功' : '评论成功')

      // Add a small delay to ensure backend transaction has committed
      await new Promise(resolve => setTimeout(resolve, 200))

      // Refresh comments list to ensure all new replies are visible
      await fetchComments()

      commentContent.value = ''
      replyToComment.value = null

      // Update resource comment count
      if (resource.value) {
        resource.value.commentCount += 1
      }
    } catch (error) {
      console.error('Failed to submit comment:', error)
      ElMessage.error('评论失败，请稍后重试')
    }
  }, { actionName: '发表评论' })
}

// Handle reply
const handleReply = (comment) => {
  replyToComment.value = comment
  // Handle case where user info might not be available
  const userDisplayName = comment.user?.nickname || comment.user?.username || `用户${comment.userId}`
  commentContent.value = `@${userDisplayName} `

  // Scroll to comment form
  const commentForm = document.querySelector('.comment-form')
  if (commentForm) {
    commentForm.scrollIntoView({ behavior: 'smooth' })
  }
}

// Handle delete comment
const handleDeleteComment = async (commentId) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteComment(commentId)

    ElMessage.success('评论已删除')

    // Refresh comments
    fetchComments()

    // Update resource comment count
    if (resource.value) {
      resource.value.commentCount -= 1
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete comment:', error)
      ElMessage.error('删除评论失败')
    }
  }
}

// Handle page change
const handlePageChange = (page) => {
  currentPage.value = page
  fetchComments()
}

// 查找目标评论所在的页面（包括回复评论）
const findTargetCommentPage = async () => {
  if (!targetCommentId.value) return null

  console.log('Searching for comment', targetCommentId.value, 'across all pages')

  // 计算总页数
  const totalPages = Math.ceil(commentTotal.value / pageSize.value)
  console.log('Total pages to search:', totalPages)

  // 搜索每一页
  for (let page = 1; page <= totalPages; page++) {
    try {
      console.log('Searching page', page)
      const response = await getCommentsByResourceId(resourceId.value, page, pageSize.value)
      const pageComments = response.data.records

      // 检查这一页的顶级评论是否包含目标评论（类型转换比较）
      const foundTopComment = pageComments.find(comment =>
        String(comment.id) === String(targetCommentId.value)
      )

      if (foundTopComment) {
        console.log('Found target comment (top-level) on page', page)
        return { page, parentId: null }
      }

      // 如果顶级评论中没找到，检查是否是回复评论
      // 我们需要加载每个顶级评论的回复来查找
      for (const topComment of pageComments) {
        if (topComment.replyCount > 0) {
          try {
            const repliesResponse = await getRepliesByParentId(topComment.id)
            const replies = repliesResponse.data

            const foundReply = replies.find(reply =>
              String(reply.id) === String(targetCommentId.value)
            )

            if (foundReply) {
              console.log('Found target comment (reply) on page', page, 'under parent', topComment.id)
              return { page, parentId: topComment.id }
            }
          } catch (replyError) {
            console.error('Error loading replies for comment', topComment.id, replyError)
          }
        }
      }
    } catch (error) {
      console.error('Error searching page', page, error)
    }
  }

  console.log('Target comment not found in any page')
  return null
}

// 确保特定父评论的回复已加载
const ensureSpecificRepliesLoaded = async (parentId) => {
  console.log('Ensuring replies for parent', parentId, 'are loaded...')

  // 查找特定父评论的"查看更多回复"按钮
  const parentElement = document.querySelector(`[data-comment-id="${parentId}"]`)
  if (parentElement) {
    const loadMoreButton = parentElement.querySelector('.load-replies .el-button')
    if (loadMoreButton && loadMoreButton.textContent.includes('查看更多回复')) {
      console.log('Clicking load more replies button for parent', parentId)
      loadMoreButton.click()
      // 等待回复加载
      await new Promise(resolve => setTimeout(resolve, 1000))
    } else {
      console.log('No load more button found for parent', parentId, 'replies might already be loaded')
    }
  } else {
    console.log('Parent element not found for ID', parentId)
  }
}

// 确保所有回复都已加载
const ensureAllRepliesLoaded = async () => {
  console.log('Ensuring all replies are loaded...')

  // 查找所有"查看更多回复"按钮并点击
  const loadMoreButtons = document.querySelectorAll('.load-replies .el-button')
  console.log('Found', loadMoreButtons.length, 'load more buttons')

  for (const button of loadMoreButtons) {
    if (button.textContent.includes('查看更多回复')) {
      console.log('Clicking load more replies button')
      button.click()
      // 等待回复加载
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }
}

// 等待目标元素出现
const waitForTargetElement = async (maxAttempts = 15) => {
  for (let i = 0; i < maxAttempts; i++) {
    const targetElement = document.querySelector(`[data-comment-id="${targetCommentId.value}"]`)
    if (targetElement) {
      console.log('Target element found after', i + 1, 'attempts')
      return targetElement
    }

    // 生产环境移除调试代码

    await new Promise(resolve => setTimeout(resolve, 300))
  }
  return null
}

// 滚动到目标评论并高亮
const scrollToTargetComment = async () => {
  console.log('scrollToTargetComment called, targetCommentId:', targetCommentId.value)

  if (!targetCommentId.value) {
    console.log('No target comment ID found')
    return
  }

  // 查找目标评论元素
  let targetElement = document.querySelector(`[data-comment-id="${targetCommentId.value}"]`)
  console.log('Target element found on current page:', targetElement)

  // 如果当前页面没找到，可能是回复评论还没加载，先尝试加载所有回复
  if (!targetElement) {
    console.log('Target not found, trying to load all replies first...')
    await ensureAllRepliesLoaded()
    await nextTick()
    targetElement = document.querySelector(`[data-comment-id="${targetCommentId.value}"]`)
    console.log('Target element found after loading replies:', targetElement)
  }

  // 如果当前页面没有找到，搜索其他页面
  if (!targetElement) {
    console.log('Target not found on current page, searching other pages...')
    const searchResult = await findTargetCommentPage()

    if (searchResult) {
      const { page: targetPage, parentId } = searchResult

      if (targetPage !== currentPage.value) {
        console.log('Switching to page', targetPage)
        currentPage.value = targetPage
        await fetchComments()
        await nextTick()
      }

      // 如果是回复评论，确保特定父评论的回复已加载
      if (parentId) {
        console.log('Target is a reply under parent', parentId, 'ensuring parent replies are loaded')
        await ensureSpecificRepliesLoaded(parentId)
      } else {
        // 如果是顶级评论，确保所有回复都已加载
        await ensureAllRepliesLoaded()
      }

      // 再次等待DOM更新
      await nextTick()

      // 强制检查DOM状态
      console.log('After page switch and reply loading, checking DOM...')
      const allCommentElements = document.querySelectorAll('[data-comment-id]')
      console.log('All comment elements found after page switch:', allCommentElements.length)
      allCommentElements.forEach((el, index) => {
        const commentId = el.getAttribute('data-comment-id')
        const isReply = el.classList.contains('reply-item')
        console.log(`  ${index + 1}. Comment ID: ${commentId} (${isReply ? 'reply' : 'top-level'})`)
      })

      targetElement = await waitForTargetElement()
    }
  }

  if (targetElement) {
    console.log('Scrolling to target comment and adding highlight')

    // 滚动到目标评论
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })

    // 添加高亮效果
    targetElement.classList.add('highlight-comment')

    // 3秒后移除高亮效果
    setTimeout(() => {
      targetElement.classList.remove('highlight-comment')
      console.log('Highlight removed')
    }, 3000)

    // 清除URL中的commentId参数，避免刷新时重复定位
    const newQuery = { ...route.query }
    delete newQuery.commentId
    router.replace({ query: newQuery })
  } else {
    console.log('Target element still not found after searching all pages')
    ElMessage.warning('未找到指定的评论，可能已被删除')
  }
}

// Fetch data on component mount
onMounted(() => {
  fetchResourceDetails()
  fetchComments()
})
</script>

<style lang="scss" scoped>
.resource-detail {
  .resource-header {
    margin-bottom: 20px;

    .resource-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .exam-type-tag {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 14px;
        color: #fff;
        margin-right: 15px;

        &.exam-type-0 {
          background-color: #409eff; // 考研 - 蓝色
        }

        &.exam-type-1 {
          background-color: #67c23a; // 考公 - 绿色
        }

        &.exam-type-2 {
          background-color: #e6a23c; // 法考 - 橙色
        }

        &.exam-type-3 {
          background-color: #f56c6c; // 教资 - 红色
        }

        &.exam-type-4 {
          background-color: #b794f6; // 其他 - 浅紫色
        }
      }

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }

    .resource-meta {
      display: flex;
      color: #909399;
      font-size: 14px;

      .meta-item {
        display: flex;
        align-items: center;
        margin-right: 20px;

        .el-icon {
          margin-right: 5px;
        }
      }
    }
  }

  .resource-actions {
    margin-bottom: 20px;

    .el-button {
      margin-right: 10px;
    }
  }

  .card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }
  }

  .resource-description {
    p {
      line-height: 1.6;
      color: #606266;
    }
  }

  .resource-comments {
    .comment-form {
      margin-bottom: 20px;

      .comment-form-actions {
        margin-top: 10px;
        text-align: right;
      }
    }

    .login-to-comment {
      margin-bottom: 20px;
      text-align: center;
      color: #909399;

      a {
        color: #409eff;
      }
    }

    .comments-list {
      .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 评论高亮效果
:deep(.highlight-comment) {
  background-color: #fff7e6 !important;
  border: 2px solid #ffa940 !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;

  // 添加轻微的阴影效果
  box-shadow: 0 4px 12px rgba(255, 169, 64, 0.3) !important;
}

@media (max-width: 768px) {
  .resource-detail {
    .resource-header {
      .resource-title {
        flex-direction: column;
        align-items: flex-start;

        .exam-type-tag {
          margin-bottom: 10px;
        }
      }

      .resource-meta {
        flex-wrap: wrap;

        .meta-item {
          margin-bottom: 10px;
        }
      }
    }

    .resource-actions {
      display: flex;
      flex-direction: column;

      .el-button {
        margin-bottom: 10px;
        margin-right: 0;
      }
    }
  }
}
</style>
