/**
 * 动态配置工具
 * 根据当前环境自动获取API基础地址
 */

/**
 * 获取API基础地址
 * @returns {string} API基础地址
 */
export const getApiBaseUrl = () => {
  // 开发环境（通过vite dev server访问）
  if (import.meta.env.DEV) {
    return '/api'  // 使用代理，由vite转发到localhost:8081
  }

  // 生产环境 - 直接写死服务器地址
  return 'http://************:8081/api'
}

/**
 * 获取文件服务基础地址
 * @returns {string} 文件服务基础地址
 */
export const getFileBaseUrl = () => {
  // 开发环境
  if (import.meta.env.DEV) {
    return 'http://localhost:8081'
  }

  // 生产环境 - 直接写死服务器地址
  return 'http://************:8081'
}

/**
 * 获取当前环境信息
 * @returns {object} 环境信息
 */
export const getEnvironmentInfo = () => {
  const isDev = import.meta.env.DEV
  const isProd = import.meta.env.PROD
  
  return {
    isDev,
    isProd,
    mode: isDev ? 'development' : 'production',
    apiBaseUrl: getApiBaseUrl(),
    fileBaseUrl: getFileBaseUrl()
  }
}

/**
 * 应用配置
 */
export const appConfig = {
  // 应用信息
  name: '考研/考公资料共享平台',
  version: '1.0.0',
  
  // API配置
  api: {
    timeout: 15000,
    baseURL: getApiBaseUrl()
  },
  
  // 文件配置
  file: {
    baseURL: getFileBaseUrl(),
    maxSize: 30 * 1024 * 1024, // 30MB
    allowedTypes: [
      // 文档类型
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',

      // 图片类型
      'image/jpeg',
      'image/png',
      'image/gif',

      // 音视频类型
      'audio/mpeg',
      'audio/mp3',
      'video/mp4',
      'video/avi',
      'video/quicktime',
      'video/x-msvideo',
      'video/x-ms-wmv',
      'video/x-flv',

      // 压缩文件类型
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed'
    ]
  },
  
  // 功能开关
  features: {
    enableDevtools: import.meta.env.DEV,
    enableMock: false,
    enableDebug: import.meta.env.DEV
  }
}

// 开发环境下打印配置信息
if (import.meta.env.DEV) {
  console.log('🔧 应用配置:', appConfig)
  console.log('🌍 环境信息:', getEnvironmentInfo())
}
